import React, { useEffect, useState } from 'react';
import { Table, Typography, Space, Spin } from 'antd';
import './HorizontalTable.scss';
import ReactMarkdown from 'react-markdown';

const { Title } = Typography;

// 表格数据在localStorage中的前缀
const TABLE_DATA_PREFIX = 'table_data_';

// 清理非当日的localStorage表格数据
const cleanupLocalStorage = (): void => {
    try {
        const today = new Date().toISOString().split('T')[0]; // 获取当前日期，格式为YYYY-MM-DD
        let cleanedCount = 0;

        // 遍历所有localStorage项
        Object.keys(localStorage).forEach(key => {
            // 只处理表格数据相关的键
            if (key.startsWith(TABLE_DATA_PREFIX)) {
                try {
                    const item = localStorage.getItem(key);
                    if (item) {
                        const data = JSON.parse(item);
                        // 检查数据是否包含创建日期且不是今天
                        if (data.createdAt && data.createdAt !== today) {
                            console.log(`清理过期表格数据: ${key}, 创建日期: ${data.createdAt}`);
                            localStorage.removeItem(key);
                            cleanedCount++;
                        }
                    }
                } catch (e) {
                    // 如果项目不是有效的JSON或没有createdAt字段，也将其删除
                    console.log(`删除无效表格数据: ${key}`);
                    localStorage.removeItem(key);
                    cleanedCount++;
                }
            }
        });

        if (cleanedCount > 0) {
            console.log(`共清理了${cleanedCount}条过期表格数据`);
        }
    } catch (error) {
        console.error('清理localStorage失败:', error);
    }
};

// 从sessionStorage获取表格数据
const getTableDataFromSessionStorage = () => {
    try {
        // 从URL获取key参数
        const urlParams = new URLSearchParams(window.location.search);
        const storageKey = urlParams.get('key');

        if (!storageKey) {
            console.error('未找到表格数据的key参数');
            return null;
        }

        // 从localStorage获取表格数据
        const tableDataJson = localStorage.getItem(storageKey);
        if (!tableDataJson) {
            console.error('未找到对应的表格数据');
            return null;
        }

        // 解析JSON数据
        try {
            const tableData = JSON.parse(tableDataJson);
            // 如果是新格式（带日期的JSON对象）
            if (tableData && typeof tableData === 'object' && tableData.content) {
                return parseMarkdownTable(tableData.content);
            }
        } catch (e) {
            // 如果解析JSON失败，可能是旧格式的数据（直接是字符串）
            console.log('使用旧格式解析表格数据');
        }

        // 旧格式兼容处理：直接解析字符串
        return parseMarkdownTable(tableDataJson);
    } catch (error) {
        console.error('获取表格数据失败:', error);
        return null;
    }
};

// 解析Markdown表格
const parseMarkdownTable = (markdown: string) => {
    const lines = markdown.split('\n').filter(line => line.trim() !== '');

    if (lines.length < 3 || !lines[0].startsWith('|') || !lines[1].startsWith('|')) {
        return { columns: [], dataSource: [] };
    }

    // 解析表头
    const headerCells = lines[0]
        .split('|')
        .slice(1, -1)
        .map(h => h.trim());
    const columns = headerCells.map((title, index) => ({
        title,
        dataIndex: `col${index}`,
        key: `col${index}`,
        ellipsis: true,
        render: text => <ReactMarkdown>{text}</ReactMarkdown>,
    }));

    // 跳过分隔行(第二行)，解析数据行
    const dataSource = lines.slice(2).map((line, rowIndex) => {
        const cells = line
            .split('|')
            .slice(1, -1)
            .map(cell => cell.trim());
        const rowData: Record<string, string> = { key: `row${rowIndex}` };

        cells.forEach((cellValue, colIndex) => {
            rowData[`col${colIndex}`] = cellValue;
        });

        return rowData;
    });

    return { columns, dataSource };
};

const HorizontalTable: React.FC = () => {
    const [tableData, setTableData] = useState<any>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // 加载组件时清理过期数据
        cleanupLocalStorage();

        const data = getTableDataFromSessionStorage();
        setTableData(data);
        setLoading(false);
    }, []);

    if (loading) {
        return (
            <div className="horizontal-table-loading">
                <Spin size="large" />
            </div>
        );
    }

    if (!tableData || tableData.columns.length === 0) {
        return (
            <div className="horizontal-table-error">
                <Space direction="vertical" align="center">
                    <Title level={4}>无法加载表格数据</Title>
                </Space>
            </div>
        );
    }

    return (
        <div className="horizontal-table-container">
            <div className="horizontal-table-content">
                <Table
                    columns={tableData.columns}
                    dataSource={tableData.dataSource}
                    pagination={false}
                    size="middle"
                    bordered
                    className="horizontal-table"
                />
            </div>
        </div>
    );
};

export default HorizontalTable;
