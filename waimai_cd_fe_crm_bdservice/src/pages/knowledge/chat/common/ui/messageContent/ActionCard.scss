.action-card {
    .action-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }

    .action-card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        margin: 0;
        line-height: 1.4;
        word-break: break-word;

        // 最多显示2行，超出省略
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .action-card-subtitle {
        font-size: 14px;
        color: #666666;
        margin: 0;
        line-height: 1.5;
        word-break: break-word;
    }

    .action-card-button-wrapper {
        .ant-btn {
            border-radius: 24px!important;
            padding: 10px 24px;
            font-weight: 500;
            font-size: 14px;

            &.ant-btn-primary {
                background-color: #ffdd10;
                border-color: #ffdd10;
                color: #333333;

                &:hover {
                    background-color: #ffcc00!important;
                    border-color: #ffcc00!important;
                }

                &:focus {
                    background-color: #ffcc00;
                    border-color: #ffcc00;
                }
            }

            &.ant-btn-default {
                background-color: #f5f5fa;
                border-color: #e0e0e0;
                color: #333333;

                &:hover {
                    background-color: #ebebeb!important;
                    border-color: #d0d0d0!important;
                }

                &:focus {
                    background-color: #ebebeb;
                    border-color: #d0d0d0;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .action-card {
        .action-card-title {
            font-size: 15px;
        }

        .action-card-subtitle {
            font-size: 13px;
        }

        .action-card-button-wrapper {
            .ant-btn {
                height: 40px;
                font-size: 13px;
            }
        }
    }
}
